{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/store/authStore';\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport PublicHeader from '@/components/layout/PublicHeader';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  return (\n    <div className=\"flex min-h-screen flex-col\">\n      {/* Hero Section */}\n      <main className=\"flex-1\">\n        <section className=\"relative overflow-hidden bg-gradient-to-br from-background via-background to-secondary/20 px-6 py-24 sm:py-32 lg:px-8\">\n          <div className=\"mx-auto max-w-7xl\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h1 className=\"text-4xl font-bold tracking-tight text-foreground sm:text-6xl font-poppins\">\n                Modern Dental Practice\n                <span className=\"block text-primary\">Management</span>\n              </h1>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Streamline your dental practice with our secure, HIPAA-compliant platform.\n                Manage appointments, patient records, and documents with ease.\n              </p>\n              <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n                <Button size=\"lg\" className=\"glass-button\">\n                  Get Started\n                </Button>\n                <Button variant=\"outline\" size=\"lg\">\n                  Learn More\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Background decoration */}\n          <div className=\"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\">\n            <div className=\"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary to-accent-secondary opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\" />\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section className=\"py-24 sm:py-32\">\n          <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins\">\n                Everything you need to manage your practice\n              </h2>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Our comprehensive platform provides all the tools you need for efficient dental practice management.\n              </p>\n            </div>\n\n            <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\">\n              <div className=\"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\">\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Appointment Management\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Streamlined booking system with real-time availability, automated reminders,\n                      and easy rescheduling for both patients and staff.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Patient Records\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Secure, encrypted patient profiles with medical history, treatment plans,\n                      and comprehensive document management.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      HIPAA Compliance\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Built-in security features, audit logging, and compliance tools\n                      to ensure your practice meets all healthcare regulations.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"mx-auto max-w-7xl px-6 py-12 lg:px-8\">\n          <div className=\"text-center\">\n            <p className=\"text-sm leading-6 text-muted-foreground\">\n              © 2024 Dentist Appointment Management Platform. Built with security and compliance in mind.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAiB;KAAO;IAE5B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAA6E;8DAEzF,6LAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;8DAAe;;;;;;8DAG3C,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAQ1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6E;;;;;;sDAG3F,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAK9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnE;GAnHwB;;QACP,qIAAA,CAAA,YAAS;QACI,4HAAA,CAAA,UAAO;;;KAFb", "debugId": null}}]}