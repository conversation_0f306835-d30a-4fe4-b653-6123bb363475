{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/layout/PublicHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { Heart } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { useAuth } from '@/store/authStore';\n\nconst PublicHeader: React.FC = () => {\n  const router = useRouter();\n  const { isAuthenticated } = useAuth();\n\n  const handleGetStarted = () => {\n    router.push('/auth/register');\n  };\n\n  const handleSignIn = () => {\n    router.push('/auth/login');\n  };\n\n  return (\n    <header className=\"sticky top-0 z-50 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center backdrop-blur-sm border border-primary/30\">\n              <Heart className=\"w-4 h-4 text-primary\" />\n            </div>\n            <div className=\"hidden sm:block\">\n              <span className=\"text-xl font-bold text-foreground font-poppins\">\n                DentCare Pro\n              </span>\n            </div>\n          </Link>\n\n          {/* Navigation Links */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"#features\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Features\n            </Link>\n            <Link\n              href=\"#pricing\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Pricing\n            </Link>\n            <Link\n              href=\"#about\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              About\n            </Link>\n            <Link\n              href=\"#contact\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Contact\n            </Link>\n          </nav>\n\n          {/* Auth Buttons */}\n          <div className=\"flex items-center space-x-3\">\n            {isAuthenticated ? (\n              <Button\n                onClick={() => router.push('/dashboard')}\n                className=\"glass-button\"\n              >\n                Dashboard\n              </Button>\n            ) : (\n              <>\n                <Button\n                  variant=\"ghost\"\n                  onClick={handleSignIn}\n                  className=\"hidden sm:inline-flex\"\n                >\n                  Sign in\n                </Button>\n                <Button\n                  onClick={handleGetStarted}\n                  className=\"glass-button\"\n                >\n                  Get Started\n                </Button>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default PublicHeader;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,eAAyB;;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD;IAElC,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAiD;;;;;;;;;;;;;;;;;kCAOrE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;kCACZ,gCACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;iDAID;;8CACE,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAvFM;;QACW,qIAAA,CAAA,YAAS;QACI,4HAAA,CAAA,UAAO;;;KAF/B;uCAyFS", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/store/authStore';\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport PublicHeader from '@/components/layout/PublicHeader';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  const handleGetStarted = () => {\n    router.push('/auth/register');\n  };\n\n  const handleLearnMore = () => {\n    // Scroll to features section\n    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <div className=\"flex min-h-screen flex-col\">\n      <PublicHeader />\n\n      {/* Hero Section */}\n      <main className=\"flex-1\">\n        <section className=\"relative overflow-hidden bg-gradient-to-br from-background via-background to-secondary/20 px-6 py-24 sm:py-32 lg:px-8\">\n          <div className=\"mx-auto max-w-7xl\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h1 className=\"text-4xl font-bold tracking-tight text-foreground sm:text-6xl font-poppins\">\n                Modern Dental Practice\n                <span className=\"block text-primary\">Management</span>\n              </h1>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Streamline your dental practice with our secure, HIPAA-compliant platform.\n                Manage appointments, patient records, and documents with ease.\n              </p>\n              <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n                <Button size=\"lg\" className=\"glass-button\" onClick={handleGetStarted}>\n                  Get Started\n                </Button>\n                <Button variant=\"outline\" size=\"lg\" onClick={handleLearnMore}>\n                  Learn More\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Background decoration */}\n          <div className=\"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\">\n            <div className=\"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary to-accent-secondary opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\" />\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section id=\"features\" className=\"py-24 sm:py-32\">\n          <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins\">\n                Everything you need to manage your practice\n              </h2>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Our comprehensive platform provides all the tools you need for efficient dental practice management.\n              </p>\n            </div>\n\n            <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\">\n              <div className=\"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\">\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Appointment Management\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Streamlined booking system with real-time availability, automated reminders,\n                      and easy rescheduling for both patients and staff.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Patient Records\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Secure, encrypted patient profiles with medical history, treatment plans,\n                      and comprehensive document management.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      HIPAA Compliance\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Built-in security features, audit logging, and compliance tools\n                      to ensure your practice meets all healthcare regulations.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Pricing Section */}\n        <section id=\"pricing\" className=\"py-24 sm:py-32 bg-secondary/20\">\n          <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins\">\n                Choose the perfect plan for your practice\n              </h2>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Flexible pricing options designed to grow with your dental practice.\n              </p>\n            </div>\n\n            <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\">\n              <div className=\"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\">\n                {/* Starter Plan */}\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Starter\n                    </CardTitle>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Perfect for small practices getting started\n                    </CardDescription>\n                    <div className=\"mt-4\">\n                      <span className=\"text-3xl font-bold text-foreground\">$49</span>\n                      <span className=\"text-muted-foreground\">/month</span>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <ul className=\"space-y-3 text-sm text-muted-foreground\">\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Up to 100 patients\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Basic appointment scheduling\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Patient records management\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Email support\n                      </li>\n                    </ul>\n                    <Button className=\"w-full mt-6 glass-button\" onClick={handleGetStarted}>\n                      Get Started\n                    </Button>\n                  </CardContent>\n                </Card>\n\n                {/* Professional Plan */}\n                <Card className=\"glass-card border-primary/50 relative\">\n                  <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                    <span className=\"bg-primary text-primary-foreground px-3 py-1 text-xs font-medium rounded-full\">\n                      Most Popular\n                    </span>\n                  </div>\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Professional\n                    </CardTitle>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Ideal for growing dental practices\n                    </CardDescription>\n                    <div className=\"mt-4\">\n                      <span className=\"text-3xl font-bold text-foreground\">$99</span>\n                      <span className=\"text-muted-foreground\">/month</span>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <ul className=\"space-y-3 text-sm text-muted-foreground\">\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Up to 500 patients\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Advanced scheduling & reminders\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Document sharing & storage\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Analytics & reporting\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Priority support\n                      </li>\n                    </ul>\n                    <Button className=\"w-full mt-6 glass-button\" onClick={handleGetStarted}>\n                      Get Started\n                    </Button>\n                  </CardContent>\n                </Card>\n\n                {/* Enterprise Plan */}\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Enterprise\n                    </CardTitle>\n                    <CardDescription className=\"text-muted-foreground\">\n                      For large practices and dental groups\n                    </CardDescription>\n                    <div className=\"mt-4\">\n                      <span className=\"text-3xl font-bold text-foreground\">$199</span>\n                      <span className=\"text-muted-foreground\">/month</span>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <ul className=\"space-y-3 text-sm text-muted-foreground\">\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Unlimited patients\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Multi-location support\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Custom integrations\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Advanced analytics\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        24/7 dedicated support\n                      </li>\n                    </ul>\n                    <Button className=\"w-full mt-6 glass-button\" onClick={handleGetStarted}>\n                      Contact Sales\n                    </Button>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"mx-auto max-w-7xl px-6 py-12 lg:px-8\">\n          <div className=\"text-center\">\n            <p className=\"text-sm leading-6 text-muted-foreground\">\n              © 2024 Dentist Appointment Management Platform. Built with security and compliance in mind.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,6BAA6B;QAC7B,SAAS,cAAc,CAAC,aAAa,eAAe;YAAE,UAAU;QAAS;IAC3E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+IAAA,CAAA,UAAY;;;;;0BAGb,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAA6E;8DAEzF,6LAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;oDAAe,SAAS;8DAAkB;;;;;;8DAGtE,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;oDAAK,SAAS;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;0CAQpE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,6LAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6E;;;;;;sDAG3F,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAK9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAY/D,6LAAC;wBAAQ,IAAG;wBAAU,WAAU;kCAC9B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6E;;;;;;sDAG3F,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAK9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;0EAG7D,6LAAC,mIAAA,CAAA,kBAAe;gEAAC,WAAU;0EAAwB;;;;;;0EAGnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG5C,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;;;;;;;0EAIlE,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAA2B,SAAS;0EAAkB;;;;;;;;;;;;;;;;;;0DAO5E,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAgF;;;;;;;;;;;kEAIlG,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;0EAG7D,6LAAC,mIAAA,CAAA,kBAAe;gEAAC,WAAU;0EAAwB;;;;;;0EAGnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG5C,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;;;;;;;0EAIlE,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAA2B,SAAS;0EAAkB;;;;;;;;;;;;;;;;;;0DAO5E,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;0EAG7D,6LAAC,mIAAA,CAAA,kBAAe;gEAAC,WAAU;0EAAwB;;;;;;0EAGnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG5C,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;;;;;;;0EAIlE,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAA2B,SAAS;0EAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYtF,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnE;GAlRwB;;QACP,qIAAA,CAAA,YAAS;QACI,4HAAA,CAAA,UAAO;;;KAFb", "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "file": "heart.js", "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/node_modules/lucide-react/src/icons/heart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z',\n      key: 'c3ymky',\n    },\n  ],\n];\n\n/**\n * @component @name Heart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTRjMS40OS0xLjQ2IDMtMy4yMSAzLTUuNUE1LjUgNS41IDAgMCAwIDE2LjUgM2MtMS43NiAwLTMgLjUtNC41IDItMS41LTEuNS0yLjc0LTItNC41LTJBNS41IDUuNSAwIDAgMCAyIDguNWMwIDIuMyAxLjUgNC4wNSAzIDUuNWw3IDdaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/heart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Heart = createLucideIcon('heart', __iconNode);\n\nexport default Heart;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}