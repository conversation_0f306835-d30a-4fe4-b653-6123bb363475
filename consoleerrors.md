react-server-dom-turbopack-client.browser.development.js:2654  Server   ⚠ Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<__next_metadata_boundary__>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2034
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<MetadataTree>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
react-server-dom-turbopack-client.browser.development.js:2654  Server   ⚠ Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<__next_metadata_boundary__>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2034
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<MetadataTree>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
GenAIWebpageEligibilityService.js:18 
            
            
           GET https://raw.githubusercontent.com/Bon-Appetit/porn-domains/refs/heads/master/block.txt 404 (Not Found)
fetchExplicitBlockList @ GenAIWebpageEligibilityService.js:18
getExplicitBlockList @ GenAIWebpageEligibilityService.js:18
await in getExplicitBlockList
_shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
await in _shouldShowTouchpoints
shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
isEligible @ ActionableCoachmark.js:18
getRenderPrompt @ ShowOneChild.js:18
await in getRenderPrompt
render @ ShowOneChild.js:18
(anonymous) @ ch-content-script-dend.js:18
await in (anonymous)
(anonymous) @ ch-content-script-dend.js:18
j @ jquery-3.1.1.min.js:2
k @ jquery-3.1.1.min.js:2
setTimeout
(anonymous) @ jquery-3.1.1.min.js:2
i @ jquery-3.1.1.min.js:2
add @ jquery-3.1.1.min.js:2
(anonymous) @ jquery-3.1.1.min.js:2
Deferred @ jquery-3.1.1.min.js:2
then @ jquery-3.1.1.min.js:2
r.fn.ready @ jquery-3.1.1.min.js:2
(anonymous) @ ch-content-script-dend.js:18
page.tsx:17  Server   ⚠ Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<Home>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
page.tsx:17  Server   ⚠ Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<Home>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
page.tsx:17  Server   ⚠ Unsupported metadata viewport is configured in metadata export in /dashboard. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:163
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<Home>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
page.tsx:17  Server   ⚠ Unsupported metadata themeColor is configured in metadata export in /dashboard. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:163
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<Home>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopSync @ react-dom-client.development.js:15077
renderRootSync @ react-dom-client.development.js:15057
performWorkOnRoot @ react-dom-client.development.js:14525
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
[project]/node_modules/next/dist/client/app-index.js [app-client] (ecmascript) @ app-index.tsx:157
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateModuleFromParent @ dev-base.ts:128
commonJsRequire @ runtime-utils.ts:241
(anonymous) @ app-next-turbopack.ts:11
(anonymous) @ app-bootstrap.ts:78
loadScriptsInSequence @ app-bootstrap.ts:20
appBootstrap @ app-bootstrap.ts:60
[project]/node_modules/next/dist/client/app-next-turbopack.js [app-client] (ecmascript) @ app-next-turbopack.ts:10
(anonymous) @ dev-base.ts:201
runModuleExecutionHooks @ dev-base.ts:261
instantiateModule @ dev-base.ts:199
getOrInstantiateRuntimeModule @ dev-base.ts:97
registerChunk @ runtime-backend-dom.ts:85
await in registerChunk
registerChunk @ runtime-base.ts:356
(anonymous) @ dev-backend-dom.ts:127
(anonymous) @ dev-backend-dom.ts:127
useRouteProtection.ts:36  Server   ⚠ Unsupported metadata viewport is configured in metadata export in /auth/login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:163
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
useRouteProtection.useEffect @ useRouteProtection.ts:36
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<DashboardPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
useRouteProtection.ts:36  Server   ⚠ Unsupported metadata themeColor is configured in metadata export in /auth/login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:163
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
useRouteProtection.useEffect @ useRouteProtection.ts:36
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<DashboardPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
useRouteProtection.ts:36  Server   ⚠ Unsupported metadata viewport is configured in metadata export in /auth/login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
useRouteProtection.useEffect @ useRouteProtection.ts:36
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<DashboardPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
useRouteProtection.ts:36  Server   ⚠ Unsupported metadata themeColor is configured in metadata export in /auth/login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
useRouteProtection.useEffect @ useRouteProtection.ts:36
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<DashboardPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
useRouteProtection.ts:36  Server   ⚠ Unsupported metadata viewport is configured in metadata export in /auth/login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
useRouteProtection.useEffect @ useRouteProtection.ts:36
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<DashboardPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
useRouteProtection.ts:36  Server   ⚠ Unsupported metadata themeColor is configured in metadata export in /auth/login. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
useRouteProtection.useEffect @ useRouteProtection.ts:36
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<DashboardPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
Home.useEffect @ page.tsx:17
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
reconnectPassiveEffects @ react-dom-client.development.js:14096
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14089
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
recursivelyTraverseReconnectPassiveEffects @ react-dom-client.development.js:14067
reconnectPassiveEffects @ react-dom-client.development.js:14143
doubleInvokeEffectsOnFiber @ react-dom-client.development.js:16099
runWithFiberInDEV @ react-dom-client.development.js:844
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16059
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16066
commitDoubleInvokeEffectsInDEV @ react-dom-client.development.js:16108
flushPassiveEffects @ react-dom-client.development.js:15878
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
GenAIWebpageEligibilityService.js:18 
            
            
           GET https://raw.githubusercontent.com/Bon-Appetit/porn-domains/refs/heads/master/block.txt 404 (Not Found)
fetchExplicitBlockList @ GenAIWebpageEligibilityService.js:18
getExplicitBlockList @ GenAIWebpageEligibilityService.js:18
await in getExplicitBlockList
_shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
await in _shouldShowTouchpoints
shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
(anonymous) @ content-script-utils.js:18
(anonymous) @ content-script-utils.js:18
j @ jquery-3.1.1.min.js:2
k @ jquery-3.1.1.min.js:2
setTimeout
(anonymous) @ jquery-3.1.1.min.js:2
i @ jquery-3.1.1.min.js:2
add @ jquery-3.1.1.min.js:2
(anonymous) @ jquery-3.1.1.min.js:2
Deferred @ jquery-3.1.1.min.js:2
then @ jquery-3.1.1.min.js:2
r.fn.ready @ jquery-3.1.1.min.js:2
renderGenAIWebpageFAB @ content-script-utils.js:18
(anonymous) @ content-script-utils.js:18
(anonymous) @ content-script-utils.js:18
await in (anonymous)
(anonymous) @ content-script-utils.js:18
AuthLayout.tsx:130  Server   ⚠ Unsupported metadata viewport is configured in metadata export in /. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:163
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
navigate @ link.tsx:289
exports.startTransition @ react.development.js:1127
linkClicked @ link.tsx:297
onClick @ link.tsx:629
executeDispatch @ react-dom-client.development.js:16501
runWithFiberInDEV @ react-dom-client.development.js:844
processDispatchQueue @ react-dom-client.development.js:16551
(anonymous) @ react-dom-client.development.js:17149
batchedUpdates$1 @ react-dom-client.development.js:3262
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16705
dispatchEvent @ react-dom-client.development.js:20815
dispatchDiscreteEvent @ react-dom-client.development.js:20783
<a>
exports.jsx @ react-jsx-runtime.development.js:339
LinkComponent @ link.tsx:716
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<LinkComponent>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
AuthLayout @ AuthLayout.tsx:130
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AuthLayout>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
LoginPage @ page.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<LoginPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
useRouteProtection.useEffect @ useRouteProtection.ts:36
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<DashboardPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
AuthLayout.tsx:130  Server   ⚠ Unsupported metadata themeColor is configured in metadata export in /. Please move it to viewport export instead.
Read more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport
react-stack-bottom-frame @ react-server-dom-turbopack-client.browser.development.js:2654
resolveConsoleEntry @ react-server-dom-turbopack-client.browser.development.js:2128
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2263
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-turbopack-client.browser.development.js:2033
initializeFakeTask @ react-server-dom-turbopack-client.browser.development.js:2020
resolveDebugInfo @ react-server-dom-turbopack-client.browser.development.js:2056
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2254
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:163
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
navigate @ link.tsx:289
exports.startTransition @ react.development.js:1127
linkClicked @ link.tsx:297
onClick @ link.tsx:629
executeDispatch @ react-dom-client.development.js:16501
runWithFiberInDEV @ react-dom-client.development.js:844
processDispatchQueue @ react-dom-client.development.js:16551
(anonymous) @ react-dom-client.development.js:17149
batchedUpdates$1 @ react-dom-client.development.js:3262
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16705
dispatchEvent @ react-dom-client.development.js:20815
dispatchDiscreteEvent @ react-dom-client.development.js:20783
<a>
exports.jsx @ react-jsx-runtime.development.js:339
LinkComponent @ link.tsx:716
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<LinkComponent>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
AuthLayout @ AuthLayout.tsx:130
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<AuthLayout>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
LoginPage @ page.tsx:97
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10555
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
<LoginPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
(anonymous) @ react-server-dom-turbopack-client.browser.development.js:2347
initializeModelChunk @ react-server-dom-turbopack-client.browser.development.js:1047
resolveModelChunk @ react-server-dom-turbopack-client.browser.development.js:1024
resolveModel @ react-server-dom-turbopack-client.browser.development.js:1592
processFullStringRow @ react-server-dom-turbopack-client.browser.development.js:2281
processFullBinaryRow @ react-server-dom-turbopack-client.browser.development.js:2226
progress @ react-server-dom-turbopack-client.browser.development.js:2472
"use server"
ResponseInstance @ react-server-dom-turbopack-client.browser.development.js:1580
createResponseFromOptions @ react-server-dom-turbopack-client.browser.development.js:2389
exports.createFromReadableStream @ react-server-dom-turbopack-client.browser.development.js:2702
createFromNextReadableStream @ fetch-server-response.ts:301
fetchServerResponse @ fetch-server-response.ts:230
await in fetchServerResponse
(anonymous) @ prefetch-cache-utils.ts:323
task @ promise-queue.ts:33
processNext @ promise-queue.ts:66
enqueue @ promise-queue.ts:46
createLazyPrefetchEntry @ prefetch-cache-utils.ts:322
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.ts:227
navigateReducer @ navigate-reducer.ts:216
clientReducer @ router-reducer.ts:32
action @ app-router-instance.ts:211
runAction @ app-router-instance.ts:98
dispatchAction @ app-router-instance.ts:185
dispatch @ app-router-instance.ts:209
(anonymous) @ use-action-queue.ts:46
startTransition @ react-dom-client.development.js:7842
dispatch @ use-action-queue.ts:45
dispatchAppRouterAction @ use-action-queue.ts:22
dispatchNavigateAction @ app-router-instance.ts:280
(anonymous) @ app-router-instance.ts:352
exports.startTransition @ react.development.js:1127
push @ app-router-instance.ts:351
useRouteProtection.useEffect @ useRouteProtection.ts:36
react-stack-bottom-frame @ react-dom-client.development.js:23054
runWithFiberInDEV @ react-dom-client.development.js:844
commitHookEffectListMount @ react-dom-client.development.js:11977
commitHookPassiveMountEffects @ react-dom-client.development.js:12098
commitPassiveMountOnFiber @ react-dom-client.development.js:13928
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13931
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13921
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:14047
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13901
commitPassiveMountOnFiber @ react-dom-client.development.js:13940
flushPassiveEffects @ react-dom-client.development.js:15868
flushPendingEffects @ react-dom-client.development.js:15829
flushSpawnedWork @ react-dom-client.development.js:15795
commitRoot @ react-dom-client.development.js:15528
commitRootWhenReady @ react-dom-client.development.js:14758
performWorkOnRoot @ react-dom-client.development.js:14681
performSyncWorkOnRoot @ react-dom-client.development.js:16364
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16210
flushPassiveEffects @ react-dom-client.development.js:15880
(anonymous) @ react-dom-client.development.js:15504
performWorkUntilDeadline @ scheduler.development.js:45
<DashboardPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.tsx:60
react-stack-bottom-frame @ react-dom-client.development.js:22973
renderWithHooksAgain @ react-dom-client.development.js:6766
renderWithHooks @ react-dom-client.development.js:6678
updateFunctionComponent @ react-dom-client.development.js:8930
beginWork @ react-dom-client.development.js:10504
runWithFiberInDEV @ react-dom-client.development.js:844
performUnitOfWork @ react-dom-client.development.js:15257
workLoopConcurrentByScheduler @ react-dom-client.development.js:15251
renderRootConcurrent @ react-dom-client.development.js:15226
performWorkOnRoot @ react-dom-client.development.js:14524
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16349
performWorkUntilDeadline @ scheduler.development.js:45
