{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// Define protected routes and their requirements\nconst protectedRoutes = {\n  // Dashboard routes\n  '/dashboard': { requireAuth: true },\n  '/patient': { requireAuth: true, roles: ['PATIENT'] },\n  '/dentist': { requireAuth: true, roles: ['DENTIST'] },\n  '/admin': { requireAuth: true, roles: ['ADMIN'] },\n  \n  // Profile and settings\n  '/profile': { requireAuth: true },\n  '/settings': { requireAuth: true },\n  \n  // Appointments\n  '/appointments': { requireAuth: true },\n  \n  // Patients (dentist/admin only)\n  '/patients': { requireAuth: true, roles: ['DENTIST', 'ADMIN'] },\n  \n  // Documents\n  '/documents': { requireAuth: true },\n  \n  // Reports (dentist/admin only)\n  '/reports': { requireAuth: true, roles: ['DENTIST', 'ADMIN'] },\n};\n\n// Public routes that don't require authentication\nconst publicRoutes = [\n  '/',\n  '/auth/login',\n  '/auth/register',\n  '/auth/forgot-password',\n  '/auth/reset-password',\n  '/legal/terms',\n  '/legal/privacy',\n  '/help',\n  '/contact',\n];\n\n// Auth routes that should redirect if already authenticated\nconst authRoutes = [\n  '/auth/login',\n  '/auth/register',\n  '/auth/forgot-password',\n];\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n  \n  // Skip middleware for static files and API routes\n  if (\n    pathname.startsWith('/_next') ||\n    pathname.startsWith('/api') ||\n    pathname.includes('.') ||\n    pathname.startsWith('/favicon')\n  ) {\n    return NextResponse.next();\n  }\n\n  // Get authentication status from cookies/headers\n  // Note: In a real implementation, you'd verify the JWT token here\n  const authToken = request.cookies.get('auth-token')?.value;\n  const userRole = request.cookies.get('user-role')?.value;\n  const isAuthenticated = !!authToken;\n\n  // Check if route is public\n  if (publicRoutes.includes(pathname)) {\n    return NextResponse.next();\n  }\n\n  // Handle auth routes (redirect if already authenticated)\n  if (authRoutes.includes(pathname) && isAuthenticated) {\n    return NextResponse.redirect(new URL('/dashboard', request.url));\n  }\n\n  // Check protected routes\n  const matchedRoute = Object.entries(protectedRoutes).find(([route]) => \n    pathname.startsWith(route)\n  );\n\n  if (matchedRoute) {\n    const [, config] = matchedRoute;\n\n    // Check authentication requirement\n    if (config.requireAuth && !isAuthenticated) {\n      const loginUrl = new URL('/auth/login', request.url);\n      loginUrl.searchParams.set('redirect', pathname);\n      return NextResponse.redirect(loginUrl);\n    }\n\n    // Check role requirements\n    if (config.roles && userRole && !config.roles.includes(userRole)) {\n      // Redirect to appropriate dashboard based on user role\n      let redirectPath = '/dashboard';\n      switch (userRole) {\n        case 'PATIENT':\n          redirectPath = '/patient/dashboard';\n          break;\n        case 'DENTIST':\n          redirectPath = '/dentist/dashboard';\n          break;\n        case 'ADMIN':\n          redirectPath = '/admin/dashboard';\n          break;\n      }\n      return NextResponse.redirect(new URL(redirectPath, request.url));\n    }\n  }\n\n  // Default: allow the request to continue\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,iDAAiD;AACjD,MAAM,kBAAkB;IACtB,mBAAmB;IACnB,cAAc;QAAE,aAAa;IAAK;IAClC,YAAY;QAAE,aAAa;QAAM,OAAO;YAAC;SAAU;IAAC;IACpD,YAAY;QAAE,aAAa;QAAM,OAAO;YAAC;SAAU;IAAC;IACpD,UAAU;QAAE,aAAa;QAAM,OAAO;YAAC;SAAQ;IAAC;IAEhD,uBAAuB;IACvB,YAAY;QAAE,aAAa;IAAK;IAChC,aAAa;QAAE,aAAa;IAAK;IAEjC,eAAe;IACf,iBAAiB;QAAE,aAAa;IAAK;IAErC,gCAAgC;IAChC,aAAa;QAAE,aAAa;QAAM,OAAO;YAAC;YAAW;SAAQ;IAAC;IAE9D,YAAY;IACZ,cAAc;QAAE,aAAa;IAAK;IAElC,+BAA+B;IAC/B,YAAY;QAAE,aAAa;QAAM,OAAO;YAAC;YAAW;SAAQ;IAAC;AAC/D;AAEA,kDAAkD;AAClD,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,4DAA4D;AAC5D,MAAM,aAAa;IACjB;IACA;IACA;CACD;AAEM,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,kDAAkD;IAClD,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,WACpB,SAAS,QAAQ,CAAC,QAClB,SAAS,UAAU,CAAC,aACpB;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,iDAAiD;IACjD,kEAAkE;IAClE,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACrD,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;IACnD,MAAM,kBAAkB,CAAC,CAAC;IAE1B,2BAA2B;IAC3B,IAAI,aAAa,QAAQ,CAAC,WAAW;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,yDAAyD;IACzD,IAAI,WAAW,QAAQ,CAAC,aAAa,iBAAiB;QACpD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,yBAAyB;IACzB,MAAM,eAAe,OAAO,OAAO,CAAC,iBAAiB,IAAI,CAAC,CAAC,CAAC,MAAM,GAChE,SAAS,UAAU,CAAC;IAGtB,IAAI,cAAc;QAChB,MAAM,GAAG,OAAO,GAAG;QAEnB,mCAAmC;QACnC,IAAI,OAAO,WAAW,IAAI,CAAC,iBAAiB;YAC1C,MAAM,WAAW,IAAI,IAAI,eAAe,QAAQ,GAAG;YACnD,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;YACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,0BAA0B;QAC1B,IAAI,OAAO,KAAK,IAAI,YAAY,CAAC,OAAO,KAAK,CAAC,QAAQ,CAAC,WAAW;YAChE,uDAAuD;YACvD,IAAI,eAAe;YACnB,OAAQ;gBACN,KAAK;oBACH,eAAe;oBACf;gBACF,KAAK;oBACH,eAAe;oBACf;gBACF,KAAK;oBACH,eAAe;oBACf;YACJ;YACA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;QAChE;IACF;IAEA,yCAAyC;IACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}