{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/layout/PublicHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { Heart } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { useAuth } from '@/store/authStore';\n\nconst PublicHeader: React.FC = () => {\n  const router = useRouter();\n  const { isAuthenticated } = useAuth();\n\n  const handleGetStarted = () => {\n    router.push('/auth/register');\n  };\n\n  const handleSignIn = () => {\n    router.push('/auth/login');\n  };\n\n  return (\n    <header className=\"sticky top-0 z-50 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center backdrop-blur-sm border border-primary/30\">\n              <Heart className=\"w-4 h-4 text-primary\" />\n            </div>\n            <div className=\"hidden sm:block\">\n              <span className=\"text-xl font-bold text-foreground font-poppins\">\n                DentCare Pro\n              </span>\n            </div>\n          </Link>\n\n          {/* Navigation Links */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"#features\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Features\n            </Link>\n            <Link\n              href=\"#pricing\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Pricing\n            </Link>\n            <Link\n              href=\"#about\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              About\n            </Link>\n            <Link\n              href=\"#contact\"\n              className=\"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              Contact\n            </Link>\n          </nav>\n\n          {/* Auth Buttons */}\n          <div className=\"flex items-center space-x-3\">\n            {isAuthenticated ? (\n              <Button\n                onClick={() => router.push('/dashboard')}\n                className=\"glass-button\"\n              >\n                Dashboard\n              </Button>\n            ) : (\n              <>\n                <Button\n                  variant=\"ghost\"\n                  onClick={handleSignIn}\n                  className=\"hidden sm:inline-flex\"\n                >\n                  Sign in\n                </Button>\n                <Button\n                  onClick={handleGetStarted}\n                  className=\"glass-button\"\n                >\n                  Get Started\n                </Button>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default PublicHeader;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,eAAyB;;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD;IAElC,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAiD;;;;;;;;;;;;;;;;;kCAOrE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;kCACZ,gCACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;iDAID;;8CACE,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAvFM;;QACW,qIAAA,CAAA,YAAS;QACI,4HAAA,CAAA,UAAO;;;KAF/B;uCAyFS", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/store/authStore';\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport PublicHeader from '@/components/layout/PublicHeader';\nimport { Calendar, FileText, Shield, Clock, Users, BarChart3 } from 'lucide-react';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated } = useAuth();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  const handleGetStarted = () => {\n    router.push('/auth/register');\n  };\n\n  const handleLearnMore = () => {\n    // Scroll to features section\n    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <div className=\"flex min-h-screen flex-col\">\n      <PublicHeader />\n\n      {/* Hero Section */}\n      <main className=\"flex-1\">\n        <section className=\"relative overflow-hidden bg-gradient-to-br from-background via-background to-secondary/20 px-6 py-24 sm:py-32 lg:px-8\">\n          <div className=\"mx-auto max-w-7xl\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h1 className=\"text-4xl font-bold tracking-tight text-foreground sm:text-6xl font-poppins\">\n                Modern Dental Practice\n                <span className=\"block text-primary\">Management</span>\n              </h1>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Streamline your dental practice with our secure, HIPAA-compliant platform.\n                Manage appointments, patient records, and documents with ease.\n              </p>\n              <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n                <Button size=\"lg\" className=\"glass-button\" onClick={handleGetStarted}>\n                  Get Started\n                </Button>\n                <Button variant=\"outline\" size=\"lg\" onClick={handleLearnMore}>\n                  Learn More\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Background decoration */}\n          <div className=\"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\">\n            <div className=\"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary to-accent-secondary opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\" />\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section id=\"features\" className=\"py-24 sm:py-32\">\n          <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins\">\n                Everything you need to manage your practice\n              </h2>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Our comprehensive platform provides all the tools you need for efficient dental practice management.\n              </p>\n            </div>\n\n            <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\">\n              <div className=\"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\">\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <div className=\"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4\">\n                      <Calendar className=\"w-6 h-6 text-primary\" />\n                    </div>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Appointment Management\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Streamlined booking system with real-time availability, automated reminders,\n                      and easy rescheduling for both patients and staff.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <div className=\"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4\">\n                      <FileText className=\"w-6 h-6 text-primary\" />\n                    </div>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Patient Records\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Secure, encrypted patient profiles with medical history, treatment plans,\n                      and comprehensive document management.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <div className=\"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4\">\n                      <Shield className=\"w-6 h-6 text-primary\" />\n                    </div>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      HIPAA Compliance\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Built-in security features, audit logging, and compliance tools\n                      to ensure your practice meets all healthcare regulations.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <div className=\"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4\">\n                      <Clock className=\"w-6 h-6 text-primary\" />\n                    </div>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Real-time Scheduling\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Live calendar updates, instant notifications, and seamless coordination\n                      between multiple dentists and treatment rooms.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <div className=\"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4\">\n                      <Users className=\"w-6 h-6 text-primary\" />\n                    </div>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Multi-user Access\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Role-based permissions for dentists, hygienists, and administrative staff\n                      with secure access controls and audit trails.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <div className=\"w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-4\">\n                      <BarChart3 className=\"w-6 h-6 text-primary\" />\n                    </div>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Analytics & Reports\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Comprehensive insights into practice performance, patient trends,\n                      and revenue analytics to help grow your business.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Pricing Section */}\n        <section id=\"pricing\" className=\"py-24 sm:py-32 bg-secondary/20\">\n          <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins\">\n                Choose the perfect plan for your practice\n              </h2>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Flexible pricing options designed to grow with your dental practice.\n              </p>\n            </div>\n\n            <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\">\n              <div className=\"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\">\n                {/* Starter Plan */}\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Starter\n                    </CardTitle>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Perfect for small practices getting started\n                    </CardDescription>\n                    <div className=\"mt-4\">\n                      <span className=\"text-3xl font-bold text-foreground\">$49</span>\n                      <span className=\"text-muted-foreground\">/month</span>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <ul className=\"space-y-3 text-sm text-muted-foreground\">\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Up to 100 patients\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Basic appointment scheduling\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Patient records management\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Email support\n                      </li>\n                    </ul>\n                    <Button className=\"w-full mt-6 glass-button\" onClick={handleGetStarted}>\n                      Get Started\n                    </Button>\n                  </CardContent>\n                </Card>\n\n                {/* Professional Plan */}\n                <Card className=\"glass-card border-primary/50 relative\">\n                  <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                    <span className=\"bg-primary text-primary-foreground px-3 py-1 text-xs font-medium rounded-full\">\n                      Most Popular\n                    </span>\n                  </div>\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Professional\n                    </CardTitle>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Ideal for growing dental practices\n                    </CardDescription>\n                    <div className=\"mt-4\">\n                      <span className=\"text-3xl font-bold text-foreground\">$99</span>\n                      <span className=\"text-muted-foreground\">/month</span>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <ul className=\"space-y-3 text-sm text-muted-foreground\">\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Up to 500 patients\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Advanced scheduling & reminders\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Document sharing & storage\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Analytics & reporting\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Priority support\n                      </li>\n                    </ul>\n                    <Button className=\"w-full mt-6 glass-button\" onClick={handleGetStarted}>\n                      Get Started\n                    </Button>\n                  </CardContent>\n                </Card>\n\n                {/* Enterprise Plan */}\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Enterprise\n                    </CardTitle>\n                    <CardDescription className=\"text-muted-foreground\">\n                      For large practices and dental groups\n                    </CardDescription>\n                    <div className=\"mt-4\">\n                      <span className=\"text-3xl font-bold text-foreground\">$199</span>\n                      <span className=\"text-muted-foreground\">/month</span>\n                    </div>\n                  </CardHeader>\n                  <CardContent>\n                    <ul className=\"space-y-3 text-sm text-muted-foreground\">\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Unlimited patients\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Multi-location support\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Custom integrations\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        Advanced analytics\n                      </li>\n                      <li className=\"flex items-center\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full mr-3\"></span>\n                        24/7 dedicated support\n                      </li>\n                    </ul>\n                    <Button className=\"w-full mt-6 glass-button\" onClick={handleGetStarted}>\n                      Contact Sales\n                    </Button>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* About Section */}\n        <section id=\"about\" className=\"py-24 sm:py-32\">\n          <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n            <div className=\"mx-auto max-w-2xl lg:max-w-none\">\n              <div className=\"grid grid-cols-1 gap-x-8 gap-y-16 lg:grid-cols-2 lg:items-center\">\n                <div>\n                  <h2 className=\"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins\">\n                    Revolutionizing dental practice management\n                  </h2>\n                  <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                    DentCare Pro was built by healthcare professionals who understand the unique challenges\n                    of running a modern dental practice. Our platform combines cutting-edge technology with\n                    intuitive design to streamline your workflow and enhance patient care.\n                  </p>\n                  <div className=\"mt-8 space-y-4\">\n                    <div className=\"flex items-start\">\n                      <div className=\"w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center mt-1 mr-4\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full\"></span>\n                      </div>\n                      <div>\n                        <h3 className=\"font-semibold text-foreground\">HIPAA Compliant</h3>\n                        <p className=\"text-muted-foreground\">Built from the ground up with healthcare security standards in mind.</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start\">\n                      <div className=\"w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center mt-1 mr-4\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full\"></span>\n                      </div>\n                      <div>\n                        <h3 className=\"font-semibold text-foreground\">Easy to Use</h3>\n                        <p className=\"text-muted-foreground\">Intuitive interface designed for busy healthcare professionals.</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-start\">\n                      <div className=\"w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center mt-1 mr-4\">\n                        <span className=\"w-2 h-2 bg-primary rounded-full\"></span>\n                      </div>\n                      <div>\n                        <h3 className=\"font-semibold text-foreground\">24/7 Support</h3>\n                        <p className=\"text-muted-foreground\">Our dedicated support team is here to help whenever you need it.</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"relative\">\n                  <div className=\"glass-card p-8\">\n                    <div className=\"space-y-6\">\n                      <div className=\"text-center\">\n                        <div className=\"text-4xl font-bold text-primary\">10,000+</div>\n                        <div className=\"text-muted-foreground\">Appointments Managed</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-4xl font-bold text-primary\">500+</div>\n                        <div className=\"text-muted-foreground\">Dental Practices</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-4xl font-bold text-primary\">99.9%</div>\n                        <div className=\"text-muted-foreground\">Uptime Guarantee</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Contact Section */}\n        <section id=\"contact\" className=\"py-24 sm:py-32 bg-secondary/20\">\n          <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins\">\n                Get in touch\n              </h2>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Ready to transform your dental practice? Contact us today to learn more about DentCare Pro.\n              </p>\n            </div>\n\n            <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\">\n              <div className=\"grid grid-cols-1 gap-x-8 gap-y-16 lg:grid-cols-3\">\n                <Card className=\"glass-card text-center\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Sales Inquiries\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <p className=\"text-muted-foreground mb-4\">\n                      Interested in DentCare Pro for your practice?\n                    </p>\n                    <p className=\"font-medium text-foreground\"><EMAIL></p>\n                    <p className=\"text-muted-foreground\">(555) 123-4567</p>\n                    <Button className=\"mt-4 glass-button\" onClick={handleGetStarted}>\n                      Schedule Demo\n                    </Button>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card text-center\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Technical Support\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <p className=\"text-muted-foreground mb-4\">\n                      Need help with your account or have technical questions?\n                    </p>\n                    <p className=\"font-medium text-foreground\"><EMAIL></p>\n                    <p className=\"text-muted-foreground\">(555) 123-4568</p>\n                    <Button variant=\"outline\" className=\"mt-4\">\n                      Contact Support\n                    </Button>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card text-center\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      General Information\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <p className=\"text-muted-foreground mb-4\">\n                      Questions about our company or partnership opportunities?\n                    </p>\n                    <p className=\"font-medium text-foreground\"><EMAIL></p>\n                    <p className=\"text-muted-foreground\">(555) 123-4569</p>\n                    <Button variant=\"outline\" className=\"mt-4\">\n                      Get in Touch\n                    </Button>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"mx-auto max-w-7xl px-6 py-12 lg:px-8\">\n          <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-4\">\n            {/* Company Info */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"flex items-center space-x-3 mb-4\">\n                <div className=\"w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center backdrop-blur-sm border border-primary/30\">\n                  <span className=\"text-primary font-bold\">D</span>\n                </div>\n                <span className=\"text-xl font-bold text-foreground font-poppins\">\n                  DentCare Pro\n                </span>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">\n                Modern dental practice management platform designed for healthcare professionals.\n              </p>\n            </div>\n\n            {/* Product Links */}\n            <div>\n              <h3 className=\"text-sm font-semibold text-foreground mb-4\">Product</h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#features\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Features\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#pricing\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Pricing\n                  </a>\n                </li>\n                <li>\n                  <a href=\"/auth/register\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Sign Up\n                  </a>\n                </li>\n                <li>\n                  <a href=\"/auth/login\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Sign In\n                  </a>\n                </li>\n              </ul>\n            </div>\n\n            {/* Support Links */}\n            <div>\n              <h3 className=\"text-sm font-semibold text-foreground mb-4\">Support</h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#contact\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Contact Us\n                  </a>\n                </li>\n                <li>\n                  <a href=\"/support\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Help Center\n                  </a>\n                </li>\n                <li>\n                  <a href=\"/docs\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Documentation\n                  </a>\n                </li>\n                <li>\n                  <a href=\"/status\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    System Status\n                  </a>\n                </li>\n              </ul>\n            </div>\n\n            {/* Legal Links */}\n            <div>\n              <h3 className=\"text-sm font-semibold text-foreground mb-4\">Legal</h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"/privacy\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Privacy Policy\n                  </a>\n                </li>\n                <li>\n                  <a href=\"/terms\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Terms of Service\n                  </a>\n                </li>\n                <li>\n                  <a href=\"/security\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    Security\n                  </a>\n                </li>\n                <li>\n                  <a href=\"/compliance\" className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\">\n                    HIPAA Compliance\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"mt-8 pt-8 border-t border-border/40\">\n            <div className=\"flex flex-col sm:flex-row justify-between items-center\">\n              <p className=\"text-sm text-muted-foreground\">\n                © 2024 DentCare Pro. All rights reserved. Built with security and compliance in mind.\n              </p>\n              <div className=\"flex space-x-6 mt-4 sm:mt-0\">\n                <a href=\"#\" className=\"text-muted-foreground hover:text-foreground transition-colors\">\n                  <span className=\"sr-only\">Twitter</span>\n                  <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84\" />\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-foreground transition-colors\">\n                  <span className=\"sr-only\">LinkedIn</span>\n                  <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z\" clipRule=\"evenodd\" />\n                  </svg>\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAO,AAAD;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,6BAA6B;QAC7B,SAAS,cAAc,CAAC,aAAa,eAAe;YAAE,UAAU;QAAS;IAC3E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+IAAA,CAAA,UAAY;;;;;0BAGb,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAQ,WAAU;;0CACjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAA6E;8DAEzF,6LAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;oDAAe,SAAS;8DAAkB;;;;;;8DAGtE,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;oDAAK,SAAS;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;0CAQpE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,6LAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6E;;;;;;sDAG3F,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAK9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;0EAEvB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAY/D,6LAAC;wBAAQ,IAAG;wBAAU,WAAU;kCAC9B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6E;;;;;;sDAG3F,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAK9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;0EAG7D,6LAAC,mIAAA,CAAA,kBAAe;gEAAC,WAAU;0EAAwB;;;;;;0EAGnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG5C,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;;;;;;;0EAIlE,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAA2B,SAAS;0EAAkB;;;;;;;;;;;;;;;;;;0DAO5E,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAgF;;;;;;;;;;;kEAIlG,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;0EAG7D,6LAAC,mIAAA,CAAA,kBAAe;gEAAC,WAAU;0EAAwB;;;;;;0EAGnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG5C,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;;;;;;;0EAIlE,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAA2B,SAAS;0EAAkB;;;;;;;;;;;;;;;;;;0DAO5E,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAwC;;;;;;0EAG7D,6LAAC,mIAAA,CAAA,kBAAe;gEAAC,WAAU;0EAAwB;;;;;;0EAGnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAG5C,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAK,WAAU;;;;;;4EAA8C;;;;;;;;;;;;;0EAIlE,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAA2B,SAAS;0EAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWpF,6LAAC;wBAAQ,IAAG;wBAAQ,WAAU;kCAC5B,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6E;;;;;;8DAG3F,6LAAC;oDAAE,WAAU;8DAA+C;;;;;;8DAK5D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;;;;;;;;;;;8EAElB,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAAgC;;;;;;sFAC9C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAGzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;;;;;;;;;;;8EAElB,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAAgC;;;;;;sFAC9C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAGzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;;;;;;;;;;;8EAElB,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAAgC;;;;;;sFAC9C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAK7C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAkC;;;;;;8EACjD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAkC;;;;;;8EACjD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAkC;;;;;;8EACjD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWvD,6LAAC;wBAAQ,IAAG;wBAAU,WAAU;kCAC9B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6E;;;;;;sDAG3F,6LAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAK9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;0EAG1C,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;0EAC3C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;gEAAoB,SAAS;0EAAkB;;;;;;;;;;;;;;;;;;0DAMrE,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;0EAG1C,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;0EAC3C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,WAAU;0EAAO;;;;;;;;;;;;;;;;;;0DAM/C,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAE,WAAU;0EAA6B;;;;;;0EAG1C,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;0EAC3C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,WAAU;0EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYzD,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;8DAE3C,6LAAC;oDAAK,WAAU;8DAAiD;;;;;;;;;;;;sDAInE,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAM/C,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAY,WAAU;kEAAwE;;;;;;;;;;;8DAIxG,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAW,WAAU;kEAAwE;;;;;;;;;;;8DAIvG,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAiB,WAAU;kEAAwE;;;;;;;;;;;8DAI7G,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAc,WAAU;kEAAwE;;;;;;;;;;;;;;;;;;;;;;;8CAQ9G,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAW,WAAU;kEAAwE;;;;;;;;;;;8DAIvG,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAW,WAAU;kEAAwE;;;;;;;;;;;8DAIvG,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAQ,WAAU;kEAAwE;;;;;;;;;;;8DAIpG,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAU,WAAU;kEAAwE;;;;;;;;;;;;;;;;;;;;;;;8CAQ1G,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6C;;;;;;sDAC3D,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAW,WAAU;kEAAwE;;;;;;;;;;;8DAIvG,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAS,WAAU;kEAAwE;;;;;;;;;;;8DAIrG,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAY,WAAU;kEAAwE;;;;;;;;;;;8DAIxG,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAc,WAAU;kEAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,MAAK;gDAAI,WAAU;;kEACpB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAE,MAAK;gDAAI,WAAU;;kEACpB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,6LAAC;4DAAK,UAAS;4DAAU,GAAE;4DAA6b,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrf;GA3kBwB;;QACP,qIAAA,CAAA,YAAS;QACI,4HAAA,CAAA,UAAO;;;KAFb", "debugId": null}}]}